'use client'

import { useQueue } from '@/hooks/useQueue'
import { QueueItem } from './QueueItem'

export function CurrentQueue() {
  const { items, currentIndex, clearQueue } = useQueue()

  if (items.length === 0) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-white">Current Queue</h2>
        </div>
        
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
              <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
            </svg>
          </div>
          <p className="text-dark-300 mb-2">Your queue is empty</p>
          <p className="text-sm text-dark-400">Add videos to start playing!</p>
        </div>
      </div>
    )
  }

  return (
    <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-white">
            Current Queue ({items.length})
          </h2>
          <button
            onClick={clearQueue}
            className="btn-secondary text-sm px-3 py-1"
            title="Clear queue"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
              <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
            </svg>
            Clear
          </button>
        </div>
      </div>
      
      <div className="max-h-96 overflow-y-auto">
        {items.map((item, index) => (
          <QueueItem
            key={`${item.id}-${item.addedAt}`}
            item={item}
            index={index}
            isActive={index === currentIndex}
            showRemove={true}
          />
        ))}
      </div>
    </div>
  )
}
