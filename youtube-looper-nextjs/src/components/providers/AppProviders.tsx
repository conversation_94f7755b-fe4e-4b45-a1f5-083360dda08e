'use client'

import { ReactNode } from 'react'
import { FirebaseProvider } from './FirebaseProvider'
import { AuthProvider } from './AuthProvider'
import { QueueProvider } from './QueueProvider'
import { NavigationProvider } from './NavigationProvider'
import { YouTubeProvider } from './YouTubeProvider'

interface AppProvidersProps {
  children: ReactNode
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <FirebaseProvider>
      <AuthProvider>
        <YouTubeProvider>
          <QueueProvider>
            <NavigationProvider>
              {children}
            </NavigationProvider>
          </QueueProvider>
        </YouTubeProvider>
      </AuthProvider>
    </FirebaseProvider>
  )
}
